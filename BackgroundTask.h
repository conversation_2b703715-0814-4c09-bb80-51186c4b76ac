#pragma once
#include <vector>
#include <thread>
#include <functional>
using namespace std;

class BackgroundTask
{
public:
    vector<int> &arr; // 引用成员变量
    BackgroundTask(vector<int> &a) : arr(a) {};
    void operator()(vector<int> &a);

    void do_lengthy_work(int a);
};

class ThreadGuard
{
    thread &t;

public:
    explicit ThreadGuard(thread &t_) : t(t_) {};
    ~ThreadGuard()
    {
        if (t.joinable())
        {
            t.join();
        }
    }
    ThreadGuard(ThreadGuard const &) = delete;
    ThreadGuard &operator=(ThreadGuard const &) = delete;
};

/**
 * 用于初始化的，只锁一次的锁
 */
class OnceInitDemo
{
    vector<int> connection_data;
    // once_flag 同步原语，用于确保某个函数或代码块在多线程环境中只被执行一次。
    std::once_flag onceFlag_connection;
    void initData(vector<int>& v){
        connection_data = move(v);
    }
public:
    void orderSend(vector<int>& v)
    {
        std::call_one(onceFlag_connection,&OnceInitDemo::initData,this)
    }
}